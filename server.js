const { createServer } = require("https");
const { parse } = require("url");
const next = require("next");
const fs = require("fs");
const path = require("path");

const dev = process.env.NODE_ENV !== "production";
const app = next({ dev });
const handle = app.getRequestHandler();

const httpsOptions = {
  key: fs.readFileSync(
    path.join(__dirname, "certificates", "auth.quote.framesound.tech+3-key.pem")
  ),
  cert: fs.readFileSync(
    path.join(__dirname, "certificates", "auth.quote.framesound.tech+3.pem")
  ),
};

// PORT可以有合理的默认值，因为这是服务器配置而非敏感信息
const PORT = process.env.PORT || 3020;

app.prepare().then(() => {
  createServer(httpsOptions, (req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  }).listen(PORT, (err) => {
    if (err) throw err;
    console.log(`> Ready on https://localhost:${PORT}`);
    console.log(`> Ready on https://auth.quote.framesound.tech:${PORT}`);
  });
});
