{"compilerOptions": {"target": "es5", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "declaration": true, "sourceMap": true, "outDir": "dist", "strict": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "jsx": "react"}, "include": ["src"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}