const { nodeResolve } = require("@rollup/plugin-node-resolve");
const commonjs = require("@rollup/plugin-commonjs");
const typescript = require("@rollup/plugin-typescript");
const dts = require("rollup-plugin-dts").default;

// 共享配置
const commonPlugins = [
  nodeResolve(),
  commonjs(),
  typescript({ tsconfig: "./tsconfig.json" }),
];

// 外部依赖
const external = [
  "react",
  "react-dom",
  "next",
  "next/server",
  "next/headers",
  "axios",
  "@fingerprintjs/fingerprintjs",
];

module.exports = [
  // 主包
  {
    input: "src/index.ts",
    output: [
      {
        file: "dist/index.js",
        format: "cjs",
        sourcemap: true,
      },
      {
        file: "dist/index.esm.js",
        format: "esm",
        sourcemap: true,
      },
    ],
    plugins: commonPlugins,
    external,
  },
  // React子包
  {
    input: "src/react/index.ts",
    output: [
      {
        file: "dist/react/index.js",
        format: "cjs",
        sourcemap: true,
      },
      {
        file: "dist/react/index.esm.js",
        format: "esm",
        sourcemap: true,
      },
    ],
    plugins: commonPlugins,
    external,
  },
  // 中间件子包
  {
    input: "src/middleware/index.ts",
    output: [
      {
        file: "dist/middleware/index.js",
        format: "cjs",
        sourcemap: true,
      },
      {
        file: "dist/middleware/index.esm.js",
        format: "esm",
        sourcemap: true,
      },
    ],
    plugins: commonPlugins,
    external,
  },
  // 类型定义
  {
    input: "src/index.ts",
    output: [{ file: "dist/index.d.ts", format: "es" }],
    plugins: [dts()],
  },
  {
    input: "src/react/index.ts",
    output: [{ file: "dist/react/index.d.ts", format: "es" }],
    plugins: [dts()],
  },
  {
    input: "src/middleware/index.ts",
    output: [{ file: "dist/middleware/index.d.ts", format: "es" }],
    plugins: [dts()],
  },
];
