{"name": "@quote/auth-client", "version": "1.0.0", "description": "Quote统一身份认证服务客户端SDK", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "rollup -c", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "prepare": "npm run build", "test": "jest"}, "keywords": ["auth", "authentication", "quote", "client", "sdk"], "author": "Quote Team", "license": "MIT", "publishConfig": {"access": "public"}, "peerDependencies": {"react": "^18.0.0", "next": "^13.4.0"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^3.4.0", "axios": "^1.4.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-typescript": "^11.1.1", "@types/jest": "^29.5.2", "@types/node": "^20.2.5", "@types/react": "^18.2.8", "jest": "^29.5.0", "rimraf": "^5.0.1", "rollup": "^3.23.0", "rollup-plugin-dts": "^5.3.0", "ts-jest": "^29.1.0", "tslib": "^2.5.3", "typescript": "^5.1.3"}, "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./react": {"import": "./dist/react/index.esm.js", "require": "./dist/react/index.js", "types": "./dist/react/index.d.ts"}, "./middleware": {"import": "./dist/middleware/index.esm.js", "require": "./dist/middleware/index.js", "types": "./dist/middleware/index.d.ts"}}}