# SSO 重定向状态管理 - 使用指南

## 概述

SDK 现在提供了标准化的 SSO 重定向状态管理功能，使用 `sessionStorage` 替代 URL 参数，提供更优雅的用户体验。

## 主要优势

- ✅ **URL 干净**：不再在 URL 中添加 `sso_redirect=true` 参数
- ✅ **自动管理**：SDK 自动处理 sessionStorage 的设置和清理
- ✅ **标准化**：所有使用 SDK 的项目都能获得一致的体验
- ✅ **向后兼容**：现有 API 保持不变

## 使用方法

### 1. 基础用法（推荐）

```typescript
import { useAuth } from "@quote/auth-client/react";

function MyPage() {
  const { checkSSORedirect, verifyStatus } = useAuth();
  const [message, setMessage] = useState("");

  useEffect(() => {
    const handleSSORedirect = async () => {
      // 检查是否是 SSO 重定向
      const ssoState = checkSSORedirect();
      
      if (ssoState.isSSORedirect) {
        console.log("检测到 SSO 重定向，验证登录状态");
        
        // 验证登录状态
        const result = await verifyStatus();
        if (result.success) {
          setMessage("登录成功！欢迎回来。");
        }
        
        // 清除 SSO 标记
        ssoState.clear();
      }
    };

    handleSSORedirect();
  }, [checkSSORedirect, verifyStatus]);

  return (
    <div>
      {message && <div className="success-message">{message}</div>}
      {/* 其他页面内容 */}
    </div>
  );
}
```

### 2. 直接使用 AuthClient

```typescript
import { AuthClient } from "@quote/auth-client";

const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL!,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL!,
});

// 检查 SSO 重定向状态
const ssoState = authClient.checkSSORedirect();

if (ssoState.isSSORedirect) {
  // 处理 SSO 重定向逻辑
  console.log("这是一个 SSO 重定向");
  
  // 清除标记
  ssoState.clear();
}
```

### 3. 与现有状态管理结合

```typescript
useEffect(() => {
  const checkAuth = async () => {
    // 检查 URL 参数（如 status=login_success）
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get("status");
    
    // 检查 SSO 重定向状态
    const ssoState = checkSSORedirect();
    
    if (ssoState.isSSORedirect) {
      // SSO 重定向：立即验证状态
      const isLoggedIn = await verifyLoginStatus();
      if (isLoggedIn) {
        setMessage("登录成功！欢迎回来。");
      }
      ssoState.clear();
    } else if (status === "login_success") {
      // 其他登录成功状态
      setMessage("登录成功！");
    } else {
      // 常规页面加载：检查本地状态
      const userStr = localStorage.getItem("user");
      if (userStr) {
        await verifyLoginStatus();
      }
    }
    
    // 清除 URL 参数
    if (status) {
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  };

  checkAuth();
}, []);
```

## API 参考

### `checkSSORedirect(): SSORedirectState`

检查当前是否是 SSO 重定向状态。

**返回值：**
```typescript
interface SSORedirectState {
  isSSORedirect: boolean;  // 是否是 SSO 重定向
  clear: () => void;       // 清除 SSO 标记的方法
}
```

**使用建议：**
- 在页面加载时调用此方法
- 如果 `isSSORedirect` 为 `true`，建议立即验证登录状态
- 处理完成后调用 `clear()` 方法清除标记

## 迁移指南

### 从 URL 参数方式迁移

**旧方式：**
```typescript
// 检查 URL 参数
const urlParams = new URLSearchParams(window.location.search);
const ssoRedirect = urlParams.get("sso_redirect");

if (ssoRedirect === "true") {
  // 处理 SSO 重定向
}
```

**新方式：**
```typescript
// 使用 SDK 方法
const ssoState = checkSSORedirect();

if (ssoState.isSSORedirect) {
  // 处理 SSO 重定向
  ssoState.clear(); // 记得清除标记
}
```

## 技术实现

- 使用 `sessionStorage` 存储 SSO 重定向标记
- 标记键名：`quote_auth_sso_redirect`
- 自动在 `login()` 方法调用时设置标记
- 提供 `clear()` 方法供开发者控制清理时机
- 完全向后兼容，不影响现有功能
