// import { NextRequest, NextResponse } from "next/server";
// import { AuthMiddlewareOptions } from "../types";

// /**
//  * 从环境变量获取允许的重定向域名列表
//  */
// function getAllowedRedirectDomains(): string[] {
//   const allowedDomainsStr = process.env.NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS;

//   if (!allowedDomainsStr) {
//     console.warn(
//       "未配置允许的重定向域名列表 NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS"
//     );
//     return [];
//   }

//   // 解析域名列表
//   const allowedDomains = allowedDomainsStr
//     .split(",")
//     .map((domain) => domain.trim())
//     .filter((domain) => domain.length > 0);

//   return allowedDomains;
// }

// /**
//  * 验证重定向URL是否安全
//  * 导出此函数供外部登录页使用
//  */
// export function isValidRedirectUrl(url: string): boolean {
//   try {
//     // 如果是相对路径，检查是否为安全的相对路径
//     if (url.startsWith("/") && !url.startsWith("//")) {
//       // 防止路径遍历攻击
//       if (url.includes("..") || url.includes("\\")) {
//         console.warn("检测到路径遍历攻击尝试:", url);
//         return false;
//       }
//       return true;
//     }

//     // 如果是绝对URL，进行域名验证
//     const redirectUrl = new URL(url);

//     // 只允许 http 和 https 协议
//     if (redirectUrl.protocol !== "http:" && redirectUrl.protocol !== "https:") {
//       console.warn("不安全的协议:", redirectUrl.protocol);
//       return false;
//     }

//     // 获取允许的域名列表
//     const allowedDomains = getAllowedRedirectDomains();

//     if (allowedDomains.length === 0) {
//       console.warn("未配置允许的重定向域名列表");
//       return false;
//     }

//     // 检查目标域名是否在允许列表中
//     const isAllowed = allowedDomains.some((allowedDomain) => {
//       // 完全匹配
//       if (redirectUrl.hostname === allowedDomain) return true;
//       // 子域名匹配（如果允许的域名以 . 开头）
//       if (
//         allowedDomain.startsWith(".") &&
//         redirectUrl.hostname.endsWith(allowedDomain)
//       ) {
//         return true;
//       }
//       // 域名后缀匹配
//       if (redirectUrl.hostname.endsWith(`.${allowedDomain}`)) {
//         return true;
//       }
//       return false;
//     });

//     if (!isAllowed) {
//       console.warn(
//         "重定向到不被允许的域名:",
//         redirectUrl.hostname,
//         "允许的域名:",
//         allowedDomains
//       );
//     }

//     return isAllowed;
//   } catch (error) {
//     console.warn("重定向URL解析失败:", url, error);
//     return false;
//   }
// }

// /**
//  * 获取安全的重定向URL
//  * 如果原始URL不安全，返回安全的默认地址
//  */
// export function getSafeRedirectUrl(
//   originalUrl: string,
//   fallbackUrl: string = "/"
// ): string {
//   try {
//     const decodedUrl = decodeURIComponent(originalUrl);

//     if (isValidRedirectUrl(decodedUrl)) {
//       return decodedUrl;
//     }

//     console.warn("不安全的重定向URL被拒绝:", decodedUrl);
//     return fallbackUrl;
//   } catch (error) {
//     console.error("解析重定向URL失败:", error);
//     return fallbackUrl;
//   }
// }

// /**
//  * 认证中间件工厂函数
//  * 创建用于保护路由的中间件
//  */
// export function authMiddleware(options: AuthMiddlewareOptions = {}) {
//   const publicPaths = options.publicPaths ?? [];
//   const fallbackUrl = options.fallbackUrl ?? "/"; // 可以有默认值

//   // 调试环境变量
//   console.log("🔧 Environment variables check:", {
//     NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL:
//       process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL,
//     NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS:
//       process.env.NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS,
//     NODE_ENV: process.env.NODE_ENV,
//   });

//   // 敏感配置强制通过环境变量配置，不提供代码配置选项
//   const authServiceRedirectUrl =
//     process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL;

//   if (!authServiceRedirectUrl) {
//     throw new Error(
//       "认证服务URL未配置，请设置NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL环境变量"
//     );
//   }

//   // 验证重定向域名配置
//   const allowedDomains = getAllowedRedirectDomains();
//   if (allowedDomains.length === 0) {
//     throw new Error(
//       "允许的重定向域名未配置，请设置NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS环境变量"
//     );
//   }

//   // 中间件初始化成功日志
//   console.log("🛡️ Auth Middleware initialized:", {
//     authServiceRedirectUrl,
//     allowedDomains,
//     publicPaths,
//     fallbackUrl,
//   });

//   // 添加默认的公开路径
//   const allPublicPaths = [
//     "/",
//     "/login",
//     "/register",
//     "/logout",
//     "/api/auth",
//     ...publicPaths,
//   ];

//   return function middleware(request: NextRequest) {
//     const { pathname } = request.nextUrl;

//     // 添加请求日志
//     console.log("🔍 Middleware processing:", {
//       pathname,
//       url: request.url,
//       hasAuthCookie: !!request.cookies.get("access_token"),
//     });

//     // 检查是否为公开路径
//     if (allPublicPaths.some((publicPath) => pathname.startsWith(publicPath))) {
//       console.log("✅ Public path, allowing access:", pathname);
//       return NextResponse.next();
//     }

//     // 检查Cookie是否存在
//     const authCookie = request.cookies.get("access_token");

//     if (!authCookie) {
//       // 获取当前URL作为重定向参数
//       const redirectUrl = request.url;

//       // 获取安全的重定向URL：如果原始URL不安全，使用配置的fallback地址
//       const safeRedirectUrl = getSafeRedirectUrl(redirectUrl, fallbackUrl);
//       const encodedRedirect = encodeURIComponent(safeRedirectUrl);

//       const loginUrl = `${authServiceRedirectUrl}/login?redirect=${encodedRedirect}`;

//       console.log("🔒 No auth cookie, redirecting to login:", {
//         originalUrl: redirectUrl,
//         safeRedirectUrl,
//         loginUrl,
//       });

//       // 重定向到登录页，始终带上安全的重定向参数
//       return NextResponse.redirect(loginUrl);
//     }

//     console.log("✅ Auth cookie found, allowing access:", pathname);
//     // Cookie存在，继续请求
//     return NextResponse.next();
//   };
// }
