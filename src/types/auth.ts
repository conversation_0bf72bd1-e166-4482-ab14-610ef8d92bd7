export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password?: string;
  code?: string;
}

export interface RegisterData extends LoginCredentials {
  name?: string;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  message?: string;
  token?: string;
}

export interface AuthClientConfig {
  authServiceApiUrl: string;
  authServiceRedirectUrl: string;
}

export interface SignInOrUpResponse {
  success: boolean;
  message?: string;
  user_id?: string;
  isNewUser?: boolean;
}
