import { authMiddleware } from "./sdk/middleware";

/**
 * 使用SDK中的中间件工厂函数创建中间件
 * 这样可以保持认证逻辑的一致性
 */
export default authMiddleware({
  // 添加项目特定的公开路径
  publicPaths: ["/"],
  // 敏感配置（authServiceRedirectUrl、authDomain）自动从环境变量读取
});

/**
 * 配置中间件匹配的路径
 */
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了:
     * - 静态文件（如图片、字体等）
     * - API路由（由API自身处理认证）
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
