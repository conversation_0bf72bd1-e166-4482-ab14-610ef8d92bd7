# 统一认证 SDK

本目录是统一认证 SDK 的导出层，实际实现在 `/sdk-package` 目录中。

## 使用方法

在项目中导入 SDK：

```typescript
// 导入 AuthClient
import { AuthClient } from "@/sdk";

// 创建客户端实例 - 严格检查环境变量
const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL!,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL!,
});

// 使用 React Hooks
import { useAuth } from "@/sdk/react";

// 使用中间件
import { authMiddleware } from "@/sdk/middleware";
```

## 迁移说明

我们正在将所有认证相关的实现统一到 `sdk-package` 中，以便于维护和复用。

原有的实现位于：

- `src/lib/services/auth`
- `src/hooks/auth`
- `src/lib/middleware`

现在这些实现已经被统一到 `sdk-package` 中，并通过 `src/sdk` 导出。

## 最佳实践

在项目中应该直接使用 SDK 包提供的功能，而不是使用原有的实现。这样可以确保所有服务使用相同的认证逻辑，避免逻辑分散和不一致。
