"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { AuthClient } from "../sdk";

// 创建认证客户端实例
const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL!,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL!,
  // 敏感域名列表，这些域名上会自动重定向到登录页
  sensitiveHostnames: [
    "quote.framesound.tech/projects",
    "admin.quote.framesound.tech",
    "dashboard.quote.framesound.tech",
  ],
  timeout: 10000, // 设置15秒超时
});

export default function HomePage() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [user, setUser] = useState<any>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [messageType, setMessageType] = useState<"success" | "error" | null>(
    null
  );

  // 验证用户登录状态
  const verifyLoginStatus = async () => {
    try {
      // 调用 verifyStatus 方法验证用户状态
      console.log("开始验证用户登录状态...");
      const result = await authClient.verifyStatus();

      // 打印验证结果
      console.log("验证状态结果:", {
        success: result.success,
        message: result.message,
        user_id: result.user_id,
      });

      if (result.success) {
        // 如果验证成功，更新用户登录状态
        console.log("验证成功，用户已登录");
        setIsAuthenticated(true);
        // 从本地存储获取用户信息
        const userStr = localStorage.getItem("user");
        if (userStr) {
          const userData = JSON.parse(userStr);
          console.log("从本地存储获取到用户信息:", userData);
          setUser(userData);
        } else if (result.user_id) {
          // 如果本地没有用户信息但有用户ID，创建基本用户对象
          const basicUser = { id: result.user_id, email: "" };
          console.log("本地无用户信息，创建基本用户对象:", basicUser);
          setUser(basicUser);
          // 保存到本地存储
          localStorage.setItem("user", JSON.stringify(basicUser));
        }
        return true;
      } else {
        // 如果验证失败，清除用户状态
        console.log("验证失败，用户未登录");
        setIsAuthenticated(false);
        localStorage.removeItem("user");
        return false;
      }
    } catch (error: any) {
      console.error("验证用户状态失败:", error);

      // 打印详细错误信息
      if (error.response) {
        console.error("错误响应状态码:", error.response.status);
        console.error("错误响应数据:", error.response.data);
      }

      // 如果是 401 错误，提示用户登录状态失效
      if (error.response && error.response.status === 401) {
        console.log("401错误: 登录状态已失效");
        setMessage("登录状态已失效，请重新登录");
        setMessageType("error");
      } else if (error.response && error.response.status === 500) {
        // 如果是 500 错误，提示用户账户状态异常
        console.log("500错误: 账户状态异常");
        setMessage("账户状态异常，请稍后再试");
        setMessageType("error");
      } else {
        // 其他错误
        console.log("未知错误:", error.message || "未知错误");
        setMessage("验证用户状态时出错，请稍后再试");
        setMessageType("error");
      }
      setIsAuthenticated(false);
      localStorage.removeItem("user");
      return false;
    }
  };

  // 检查认证状态
  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log("页面加载，开始检查认证状态...");
        // 检查URL参数中是否有状态消息
        const urlParams = new URLSearchParams(window.location.search);
        const status = urlParams.get("status");

        // 使用SDK检查SSO重定向状态
        const ssoState = authClient.checkSSORedirect();
        console.log("SSO重定向状态:", ssoState);

        // 如果是从SSO服务重定向回来的，立即验证登录状态
        if (ssoState.isSSORedirect) {
          console.log("检测到SSO重定向，立即验证登录状态");
          const isLoggedIn = await verifyLoginStatus();
          console.log("SSO重定向验证结果:", isLoggedIn);
          if (isLoggedIn) {
            setMessage("登录成功！欢迎回来。");
            setMessageType("success");
          }
          // 清除SSO重定向标记
          ssoState.clear();
        } else {
          // 否则检查本地存储中是否有用户信息
          console.log("检查本地存储中的用户信息");
          const userStr = localStorage.getItem("user");
          if (userStr) {
            console.log("本地存储中有用户信息，验证登录状态");
            // 即使有本地用户信息，也验证一下登录状态
            await verifyLoginStatus();
          } else {
            console.log("本地存储中没有用户信息，设置为未登录状态");
            setIsAuthenticated(false);
          }
        }

        // 处理其他状态消息
        if (status === "login_success") {
          console.log("检测到登录成功状态参数");
          setMessage("登录成功！欢迎回来。");
          setMessageType("success");
        } else if (status === "logout_success") {
          console.log("检测到登出成功状态参数");
          setMessage("您已成功退出登录。");
          setMessageType("success");
        }

        // 清除URL参数（只需要清除status参数，SSO状态已通过sessionStorage处理）
        if (status) {
          console.log("清除URL参数");
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );
        }
      } catch (error) {
        console.error("认证检查失败:", error);
        setIsAuthenticated(false);
      }
    };

    checkAuth();
  }, []);

  // 处理登出
  const handleLogout = async () => {
    try {
      await authClient.logout();
      // 登出后会自动重定向到登出页面
    } catch (error) {
      console.error("登出失败:", error);
      setMessage("登出失败，请稍后再试。");
      setMessageType("error");
    }
  };

  // 加载状态
  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900">
              Quote 统一认证服务
            </h1>
            <div>
              {isAuthenticated ? (
                <button
                  onClick={handleLogout}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  退出登录
                </button>
              ) : (
                <Link
                  href="/login"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  登录
                </Link>
              )}
            </div>
          </div>

          {message && (
            <div
              className={`mb-6 p-4 rounded-md ${
                messageType === "success"
                  ? "bg-green-50 text-green-800"
                  : "bg-red-50 text-red-800"
              }`}
            >
              {message}
            </div>
          )}

          <div className="border-t border-gray-200 pt-6">
            {isAuthenticated ? (
              <div>
                <h2 className="text-xl font-semibold text-gray-800 mb-4">
                  用户信息
                </h2>
                <div className="bg-gray-50 p-4 rounded-md">
                  <p className="mb-2">
                    <span className="font-medium">用户ID:</span>{" "}
                    {user?.id || "未知"}
                  </p>
                  <p className="mb-2">
                    <span className="font-medium">邮箱:</span>{" "}
                    {user?.email || "未知"}
                  </p>
                  <p>
                    <span className="font-medium">认证状态:</span>{" "}
                    <span className="text-green-600 font-medium">已认证</span>
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">
                  未登录
                </h2>
                <p className="text-gray-600 mb-6">
                  请登录以查看您的账户信息和使用我们的服务。
                </p>
                <Link
                  href="/login"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  前往登录
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
