"use client";

import React, { useEffect } from "react";
import { AuthLayout } from "../../components/auth/AuthLayout";
import { useRouter } from "next/navigation";

export default function LogoutPage() {
  const router = useRouter();

  useEffect(() => {
    // 清除本地存储的用户信息（以防万一）
    if (typeof window !== "undefined") {
      localStorage.removeItem("user");
    }

    // 延迟3秒后重定向到首页
    const timer = setTimeout(() => {
      router.push("/?status=logout_success");
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <AuthLayout title="已登出">
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <div className="mb-4">
          <svg
            className="mx-auto h-12 w-12 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">登出成功</h2>
        <p className="text-gray-600 mb-4">您已成功退出登录</p>
        <p className="text-sm text-gray-500">3秒后将自动跳转到首页...</p>
      </div>
    </AuthLayout>
  );
}
