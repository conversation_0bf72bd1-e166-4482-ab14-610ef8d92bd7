"use client";

import React, { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { AuthClient } from "../../sdk";

// 创建认证客户端实例
const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL!,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL!,
  timeout: 10000, // 设置15秒超时
});

interface LoginFormProps {
  onSuccess?: (redirectUrl?: string) => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirect = searchParams.get("redirect");

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [contact, setContact] = useState("");
  const [code, setCode] = useState("");
  const [codeSent, setCodeSent] = useState(false);
  const [codeSending, setCodeSending] = useState(false);
  const [codeCountdown, setCodeCountdown] = useState(0);

  // 处理联系方式输入变化
  const handleContactChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setContact(e.target.value);
    setError(null);
  };

  // 处理验证码输入变化
  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCode(e.target.value);
    setError(null);
  };

  // 处理倒计时
  React.useEffect(() => {
    let timer: NodeJS.Timeout;
    if (codeCountdown > 0) {
      timer = setTimeout(() => {
        setCodeCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [codeCountdown]);

  // 发送验证码
  const handleSendCode = async () => {
    if (!contact) {
      setError("Please enter your email address or phone number");
      return;
    }

    if (codeCountdown > 0) {
      setError(
        `Please wait ${codeCountdown} seconds before requesting a new code`
      );
      return;
    }

    try {
      setCodeSending(true);
      setError(null);

      await authClient.apiSendVerificationCode(contact);

      setCodeSent(true);

      // 设置倒计时（默认60秒）
      setCodeCountdown(60);
    } catch (err: any) {
      // 处理API错误响应
      if (err.response && err.response.data) {
        const { detail } = err.response.data;
        setError(
          detail || "Failed to send verification code. Please try again."
        );

        // 如果错误消息包含等待时间，提取并设置倒计时
        const waitTimeMatch = detail && detail.match(/等待(\d+)秒/);
        if (waitTimeMatch && waitTimeMatch[1]) {
          setCodeCountdown(parseInt(waitTimeMatch[1]));
        }
      } else {
        setError("Failed to send verification code. Please try again.");
      }
    } finally {
      setCodeSending(false);
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!contact) {
      setError("Please enter your email address or phone number");
      return;
    }

    if (!code) {
      setError("Please enter the verification code");
      return;
    }

    try {
      setIsLoading(true);

      // 调用登录或注册API
      const response = await authClient.signInOrUp(contact, code);

      if (!response.success) {
        throw new Error(response.message || "Login failed");
      }

      // 登录成功
      if (onSuccess) {
        onSuccess(redirect || undefined);
      } else if (redirect) {
        // 重定向到原始请求页面（SDK已自动设置SSO标记）
        window.location.href = decodeURIComponent(redirect);
      } else {
        // 默认重定向到项目页面，只保留状态参数
        window.location.href =
          "https://quote.framesound.tech:9000/projects?status=login_success";
      }
    } catch (err: any) {
      // 处理API错误响应
      if (err.response && err.response.data) {
        const { detail } = err.response.data;
        setError(detail || "Login failed. Please try again.");
      } else {
        setError(
          err instanceof Error ? err.message : "Login failed. Please try again."
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <form
        onSubmit={handleSubmit}
        className="bg-white rounded-lg shadow-md p-6"
      >
        <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">
          Sign In or Register
        </h2>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm">
            {error}
          </div>
        )}

        <Input
          label="Email Address or Phone"
          name="contact"
          type="text"
          value={contact}
          onChange={handleContactChange}
          fullWidth
          placeholder="<EMAIL> or +1234567890"
          autoComplete="email"
          disabled={isLoading}
        />

        <div className="mb-4">
          <div className="flex items-center mb-2">
            <Input
              label="Verification Code"
              name="code"
              type="text"
              value={code}
              onChange={handleCodeChange}
              fullWidth
              placeholder="Enter verification code"
              disabled={isLoading}
            />
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={handleSendCode}
              disabled={codeSending || codeCountdown > 0}
              className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400"
            >
              {codeSent
                ? codeCountdown > 0
                  ? `Resend in ${codeCountdown}s`
                  : "Resend code"
                : "Send verification code"}
            </button>
          </div>
        </div>

        <Button type="submit" fullWidth isLoading={isLoading}>
          {codeSent ? "Sign In" : "Continue"}
        </Button>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            By continuing, you agree to our Terms of Service and Privacy Policy.
          </p>
        </div>
      </form>
    </div>
  );
};
