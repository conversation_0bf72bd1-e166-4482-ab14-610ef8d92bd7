# Quote 统一身份认证服务

这是一个基于 Next.js 的统一身份认证服务，为 Quote 系列应用提供集中式的用户认证功能。

## 功能特性

- 统一的登录/注册界面
- 无密码登录/注册（基于验证码）
- 基于 HttpOnly Cookie 的安全认证
- 轻量级客户端 SDK，易于集成
- 支持 Next.js 中间件集成
- 设备指纹和安全验证

## 技术栈

- Next.js 13.4+
- TypeScript
- Tailwind CSS
- JWT 认证

## 环境要求

- Node.js 18.0+
- npm 8.0+

## 安装与设置

1. 克隆仓库

```bash
git clone https://github.com/your-username/quote-unified-auth.git
cd quote-unified-auth
```

2. 安装依赖

```bash
npm install
```

3. 配置环境变量

复制`.env.example`文件为`.env.local`，并根据需要修改配置：

```bash
cp .env.example .env.local
```

主要环境变量说明：

- `NEXT_PUBLIC_AUTH_SERVICE_API_URL`: 后端 API 基础 URL
- `NEXT_PUBLIC_AUTH_DOMAIN`: 认证服务域名
- `NEXT_PUBLIC_AUTH_SERVICE_URL`: 认证服务 URL
- `JWT_SECRET`: JWT 密钥（生产环境中应使用强随机字符串）
- `COOKIE_DOMAIN`: Cookie 域名设置
- `ALLOWED_ORIGINS`: 允许的跨域源，逗号分隔

4. 启动开发服务器

```bash
npm run dev
```

服务将在 http://localhost:3010 启动。

## 开发环境配置

### 本地 hosts 设置

为了在本地开发环境中测试跨域 Cookie，建议在 hosts 文件中添加以下映射：

```
127.0.0.1 auth.quote.framesound.tech
127.0.0.1 quote.framesound.tech
```

## API 端点

认证服务提供以下 API 端点：

### 1. 发送验证码

```
POST /auth/v1/send_verification
```

请求参数：

```json
{
  "contact": "string", // 邮箱地址或手机号
  "browser_fingerprinting": "string", // 浏览器指纹
  "device_id": "string" // 设备ID
}
```

### 2. 登录或注册

```
POST /auth/v1/sign_in_or_up
```

请求参数：

```json
{
  "contact": "string", // 邮箱地址或手机号
  "code": "string", // 验证码
  "browser_fingerprinting": "string", // 浏览器指纹
  "device_id": "string" // 设备ID
}
```

返回状态码：

- 200: 登录成功（已有用户）
- 201: 注册成功（新用户）
- 400: 请求参数错误
- 460: 验证码错误
- 461: 账户不存在
- 500: 系统异常

### 3. 登出

```
POST /auth/v1/logout
```

### 4. 验证状态

```
GET /auth/v1/verify_status
```

## 客户端集成

### 1. 安装客户端 SDK

```bash
npm install @quote/auth-client
```

### 2. 初始化客户端

```typescript
import { AuthClient } from "@quote/auth-client";

const authClient = new AuthClient({
  authServiceApiUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL,
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_URL,
});
```

### 3. 使用 React Hook

```typescript
import { useAuth } from "@quote/auth-client/react";

function MyComponent() {
  const { isAuthenticated, user, login, logout } = useAuth();

  if (!isAuthenticated) {
    return <button onClick={login}>登录</button>;
  }

  return (
    <div>
      <p>欢迎，{user?.name}</p>
      <button onClick={logout}>登出</button>
    </div>
  );
}
```

### 4. 使用中间件保护路由

在 Next.js 项目中配置中间件：

```typescript
// middleware.ts
import { authMiddleware } from "@quote/auth-client/middleware";

export default authMiddleware({
  publicPaths: ["/about", "/contact"],
  authServiceRedirectUrl: process.env.NEXT_PUBLIC_AUTH_SERVICE_URL,
});

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
```

## 部署

### 生产环境构建

```bash
npm run build
```

### 启动生产服务

```bash
npm start
```

## 许可证

MIT
