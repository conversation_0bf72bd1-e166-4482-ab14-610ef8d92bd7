/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // 开发环境的域名配置（移到环境变量中使用）
  // 不再使用不被识别的allowedDevOrigins配置

  // Next.js 不直接支持 webSocketServer 配置，可通过 webpack devServer 配置实现
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // 在开发环境中客户端配置 webpack-dev-server
      config.devServer = {
        ...(config.devServer || {}),
        host: "0.0.0.0",
        hot: true,
      };
    }
    return config;
  },
  async headers() {
    // 严格检查环境变量，不使用默认值
    if (!process.env.ALLOWED_ORIGINS) {
      throw new Error("ALLOWED_ORIGINS环境变量未配置，请设置允许的跨域源");
    }

    const allowedOrigins = process.env.ALLOWED_ORIGINS.split(",");

    return [
      {
        source: "/api/:path*",
        headers: [
          {
            key: "Access-Control-Allow-Credentials",
            value: "true",
          },
          {
            key: "Access-Control-Allow-Origin",
            // 如果只有一个源且为*，直接使用*
            // 否则使用配置的源
            value:
              allowedOrigins.length === 1 && allowedOrigins[0] === "*"
                ? "*"
                : process.env.ALLOWED_ORIGINS,
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET,OPTIONS,PATCH,DELETE,POST,PUT",
          },
          {
            key: "Access-Control-Allow-Headers",
            value:
              "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version",
          },
        ],
      },
    ];
  },
  // 处理预检请求
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: "/api/:path*",
      },
    ];
  },
};

module.exports = nextConfig;
