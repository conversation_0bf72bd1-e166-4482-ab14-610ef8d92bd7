# 统一身份认证服务 API URL
NEXT_PUBLIC_AUTH_SERVICE_API_URL=

# 统一身份认证服务重定向跳转 URL
NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL=

# JWT密钥（仅用于开发环境）
JWT_SECRET=

# Cookie域名设置（确保Cookie在所有子域名间共享）
COOKIE_DOMAIN=

# 允许的跨域源（子域名间的请求）
ALLOWED_ORIGINS=





# ================================暂时废弃============================================
# 允许的重定向域名列表（逗号分隔，支持子域名）
# 示例：example.com,.trusted.com,app.example.com
# 注意：必须包含所有需要重定向的域名，包括认证服务域名
# NEXT_PUBLIC_ALLOWED_REDIRECT_DOMAINS=