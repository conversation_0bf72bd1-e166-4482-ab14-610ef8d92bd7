{"name": "quote-unified-auth", "version": "1.0.0", "scripts": {"dev": "next dev", "dev:https": "node server.js", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^3.4.2", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "cookie": "^0.5.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "next": "^13.4.19", "postcss": "^8", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "~3.4.17"}, "devDependencies": {"@types/bcryptjs": "^2.4.3", "@types/cookie": "^0.5.1", "@types/js-cookie": "^3.0.3", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.5.9", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "autoprefixer": "^10.4.15", "eslint": "^8.48.0", "typescript": "^5.2.2"}}